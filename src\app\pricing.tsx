"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { motion } from "framer-motion";

export default function PricingPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-yellow-50 to-green-50 py-20">
      <div className="container mx-auto px-4">
        <motion.h1
          initial={{ opacity: 0, y: 40 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.7 }}
          className="text-5xl lg:text-6xl font-bold text-center mb-12 text-gray-900"
        >
          Simple, Transparent Pricing
        </motion.h1>
        <div className="grid md:grid-cols-3 gap-8">
          {/* Starter Plan */}
          <motion.div
            initial={{ opacity: 0, y: 40 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2, duration: 0.7 }}
          >
            <Card className="shadow-xl border-0 bg-white hover:scale-105 transition-transform">
              <CardContent className="p-8">
                <h2 className="text-2xl font-bold mb-4">Starter</h2>
                <div className="text-4xl font-bold mb-2">$999</div>
                <div className="text-gray-500 mb-6">per month</div>
                <ul className="mb-8 space-y-2 text-gray-700">
                  <li>Up to 2 agents</li>
                  <li>Email & chat support</li>
                  <li>Basic reporting</li>
                  <li>24/7 coverage</li>
                </ul>
                <Button className="w-full bg-black text-white rounded-full">
                  Get Started
                </Button>
              </CardContent>
            </Card>
          </motion.div>
          {/* Growth Plan */}
          <motion.div
            initial={{ opacity: 0, y: 40 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4, duration: 0.7 }}
          >
            <Card className="shadow-2xl border-2 border-yellow-400 bg-yellow-50 hover:scale-105 transition-transform">
              <CardContent className="p-8">
                <h2 className="text-2xl font-bold mb-4">Growth</h2>
                <div className="text-4xl font-bold mb-2">$2,499</div>
                <div className="text-gray-500 mb-6">per month</div>
                <ul className="mb-8 space-y-2 text-gray-700">
                  <li>Up to 6 agents</li>
                  <li>Omnichannel support</li>
                  <li>Advanced reporting</li>
                  <li>Dedicated manager</li>
                </ul>
                <Button className="w-full bg-yellow-400 text-black rounded-full hover:bg-yellow-500">
                  Get Started
                </Button>
              </CardContent>
            </Card>
          </motion.div>
          {/* Enterprise Plan */}
          <motion.div
            initial={{ opacity: 0, y: 40 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6, duration: 0.7 }}
          >
            <Card className="shadow-xl border-0 bg-white hover:scale-105 transition-transform">
              <CardContent className="p-8">
                <h2 className="text-2xl font-bold mb-4">Enterprise</h2>
                <div className="text-4xl font-bold mb-2">Custom</div>
                <div className="text-gray-500 mb-6">Contact us</div>
                <ul className="mb-8 space-y-2 text-gray-700">
                  <li>Unlimited agents</li>
                  <li>All channels</li>
                  <li>Custom integrations</li>
                  <li>Premium support</li>
                </ul>
                <Button className="w-full bg-black text-white rounded-full">
                  Contact Sales
                </Button>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </div>
    </div>
  );
}
