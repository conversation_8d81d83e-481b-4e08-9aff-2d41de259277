import { useState, useRef, useEffect } from "react";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { ChevronDown, Wifi, Cog, ShieldCheck, BarChart3 } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";

type DropdownItem = {
  name: string;
  href: string;
  icon?: React.ReactNode;
};

type DropdownProps = {
  title: string;
  items: DropdownItem[];
  isOpen: boolean;
  onOpen: () => void;
  onClose: () => void;
  hasIcons?: boolean;
  submenu?: Record<string, DropdownItem[]>;
};

const solutionsSubmenus = {
  "Customer Support": [
    { name: "General Support", href: "/solutions/customer-support/general" },
    {
      name: "Call Center Support",
      href: "/solutions/customer-support/call-center",
    },
    {
      name: "Technical Support",
      href: "/solutions/customer-support/technical",
    },
    {
      name: "Live Chat Support",
      href: "/solutions/customer-support/live-chat",
    },
    { name: "Email Support", href: "/solutions/customer-support/email" },
  ],
  "Digital Operations": [
    {
      name: "Process Automation",
      href: "/solutions/digital-operations/automation",
    },
    { name: "Data Entry", href: "/solutions/digital-operations/data-entry" },
    {
      name: "Content Moderation",
      href: "/solutions/digital-operations/moderation",
    },
  ],
  "Trust & Safety": [
    { name: "Fraud Prevention", href: "/solutions/trust-safety/fraud" },
    { name: "Content Review", href: "/solutions/trust-safety/content" },
    { name: "Risk Management", href: "/solutions/trust-safety/risk" },
  ],
  "Data & AI": [
    { name: "Data Labeling", href: "/solutions/data-ai/labeling" },
    { name: "AI Training", href: "/solutions/data-ai/training" },
    { name: "Analytics", href: "/solutions/data-ai/analytics" },
  ],
};

const Dropdown = ({
  title,
  items,
  isOpen,
  onOpen,
  onClose,
  hasIcons = false,
  submenu,
}: DropdownProps) => {
  const [submenuOpen, setSubmenuOpen] = useState<string | null>(null);
  return (
    <div
      className="relative"
      onMouseEnter={onOpen}
      onMouseLeave={() => {
        onClose();
        setSubmenuOpen(null);
      }}
    >
      <button
        className={`flex items-center space-x-1 cursor-pointer hover:text-yellow-600 transition-colors ${
          isOpen ? "text-yellow-600" : ""
        }`}
        tabIndex={0}
        onFocus={onOpen}
        onBlur={onClose}
      >
        <span>{title}</span>
        <ChevronDown
          className={`w-4 h-4 transition-transform ${
            isOpen ? "rotate-180" : ""
          }`}
        />
      </button>
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            transition={{ duration: 0.18 }}
            className="absolute top-full left-0 mt-2 w-64 bg-white rounded-lg shadow-xl border border-gray-100 py-2 z-30"
          >
            {items.map((item: any, i: number) => (
              <div
                key={i}
                className="relative group"
                onMouseEnter={() => submenu && setSubmenuOpen(item.name)}
                onMouseLeave={() => submenu && setSubmenuOpen(null)}
              >
                <Link
                  href={item.href}
                  className="flex items-center space-x-3 px-4 py-2 hover:bg-gray-50 transition-colors group"
                >
                  {hasIcons && item.icon}
                  <span className="group-hover:text-yellow-600 transition-colors">
                    {item.name}
                  </span>
                  {submenu && submenu[item.name] && (
                    <ChevronDown className="w-4 h-4 ml-auto rotate-[-90deg] text-gray-400" />
                  )}
                </Link>
                {submenu && submenu[item.name] && (
                  <AnimatePresence>
                    {submenuOpen === item.name && (
                      <motion.div
                        initial={{ opacity: 0, x: 10 }}
                        animate={{ opacity: 1, x: 0 }}
                        exit={{ opacity: 0, x: 10 }}
                        transition={{ duration: 0.18 }}
                        className="absolute top-0 left-full ml-2 w-60 bg-white rounded-lg shadow-xl border border-gray-100 py-2 z-40"
                      >
                        {submenu[item.name].map((sub: any, j: number) => (
                          <Link
                            key={j}
                            href={sub.href}
                            className="block px-4 py-2 hover:bg-gray-50 text-gray-700"
                          >
                            {sub.name}
                          </Link>
                        ))}
                      </motion.div>
                    )}
                  </AnimatePresence>
                )}
              </div>
            ))}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export const Header = () => {
  const [activeDropdown, setActiveDropdown] = useState<string | null>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const solutionsDropdown = [
    {
      name: "Customer Support",
      icon: <Wifi className="w-5 h-5 text-cyan-500" />,
      href: "#",
    },
    {
      name: "Digital Operations",
      icon: <Cog className="w-5 h-5 text-green-500" />,
      href: "#",
    },
    {
      name: "Trust & Safety",
      icon: <ShieldCheck className="w-5 h-5 text-purple-500" />,
      href: "#",
    },
    {
      name: "Data & AI",
      icon: <BarChart3 className="w-5 h-5 text-red-500" />,
      href: "#",
    },
  ];

  const industriesDropdown = [
    { name: "Gaming", href: "#" },
    { name: "Crypto", href: "#" },
    { name: "Ecommerce", href: "#" },
    { name: "Health & Wellness", href: "#" },
    { name: "SaaS", href: "#" },
    { name: "Fintech", href: "#" },
    { name: "Edtech", href: "#" },
    { name: "Online Subscriptions", href: "#" },
    { name: "Rewards & GPT Platforms", href: "#" },
  ];

  const companyDropdown = [
    { name: "About", href: "#" },
    { name: "Careers", href: "#" },
    { name: "News", href: "#" },
    { name: "Contact", href: "#" },
  ];

  const resourcesDropdown = [
    { name: "Blog", href: "#" },
    { name: "Case Studies", href: "#" },
    { name: "Documentation", href: "#" },
    { name: "Support", href: "#" },
    { name: "FAQs", href: "#" },
  ];

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setActiveDropdown(null);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  const toggleDropdown = (name: string) => {
    setActiveDropdown(activeDropdown === name ? null : name);
  };

  return (
    <header className="fixed top-0 w-full bg-white/95 backdrop-blur-sm z-50 border-b border-gray-100">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-gradient-to-r from-yellow-400 to-orange-400 rounded-full flex items-center justify-center">
              <span className="text-white font-bold text-sm">B</span>
            </div>
            <span className="text-2xl font-bold text-gray-900">B360</span>
          </div>

          <nav
            className="hidden md:flex items-center space-x-8"
            ref={dropdownRef}
          >
            <Dropdown
              title="Solutions"
              items={solutionsDropdown}
              isOpen={activeDropdown === "solutions"}
              onOpen={() => setActiveDropdown("solutions")}
              onClose={() => setActiveDropdown(null)}
              hasIcons
              submenu={solutionsSubmenus}
            />

            <Dropdown
              title="Industries"
              items={industriesDropdown}
              isOpen={activeDropdown === "industries"}
              onOpen={() => setActiveDropdown("industries")}
              onClose={() => setActiveDropdown(null)}
            />

            <Link href="#" className="hover:text-yellow-600 transition-colors">
              Our Agents
            </Link>
            <Link
              href="/pricing"
              className="hover:text-yellow-600 transition-colors"
            >
              Pricing
            </Link>

            <Dropdown
              title="Company"
              items={companyDropdown}
              isOpen={activeDropdown === "company"}
              onOpen={() => setActiveDropdown("company")}
              onClose={() => setActiveDropdown(null)}
            />

            <Dropdown
              title="Resources"
              items={resourcesDropdown}
              isOpen={activeDropdown === "resources"}
              onOpen={() => setActiveDropdown("resources")}
              onClose={() => setActiveDropdown(null)}
            />
          </nav>

          <Button className="bg-black text-white hover:bg-gray-800 rounded-full px-6">
            Get Started
          </Button>
        </div>
      </div>
    </header>
  );
};
