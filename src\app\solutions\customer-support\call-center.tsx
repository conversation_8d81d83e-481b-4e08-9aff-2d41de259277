"use client";

import { motion } from "framer-motion";

export default function CallCenterSupportPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-cyan-50 to-yellow-50 py-20">
      <div className="container mx-auto px-4">
        <motion.h1
          initial={{ opacity: 0, y: 40 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.7 }}
          className="text-5xl lg:text-6xl font-bold text-center mb-12 text-gray-900"
        >
          Call Center Support
        </motion.h1>
        <motion.p
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2, duration: 0.7 }}
          className="text-xl text-center text-gray-700 max-w-2xl mx-auto"
        >
          Our call center teams deliver seamless voice support, ensuring your
          customers always have a human to talk to, 24/7.
        </motion.p>
      </div>
    </div>
  );
}
